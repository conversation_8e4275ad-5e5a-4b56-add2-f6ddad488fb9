# 📝 2025-06-06 인증 로직 리팩터링 및 OAuth2.0 연동 작업 회고

## 📌 작업 배경

기존의 인증 시스템은 로컬 로그인(JWT 기반)만 제공하고 있었으며, 테스트 커버리지가 부족하고 예외 처리 구조가 과도하게 복잡한 문제가 있었다. 이에 따라 다음을 목표로 작업을 진행했다:

- ✅ 로컬 로그인 로직의 명확화 및 예외 단순화  
- ✅ 인증 흐름에 대한 통합 테스트 확보  
- ✅ Google OAuth2.0 로그인 기능 추가  
- ✅ 인증 성공 후 클라이언트 홈(`/`) 리디렉트 처리  

---

## 🚀 주요 작업 내역

### 1. UserServiceImpl 리팩터링
- `login()` 메소드 내에서 이메일, 탈퇴 여부, 비밀번호 일치 여부를 검증  
- 비밀번호 암호화 검증 실패 시 `401 Unauthorized` 반환  
- 더 이상 `GlobalException`, `ErrorCode` 사용하지 않고 `IllegalArgumentException`으로 단순화  

### 2. JWT 기반 로그인 통합 테스트 작성
- `/api/v1/users/login` 테스트 케이스 작성  
  - 정상 로그인  
  - 이메일 불일치 → 401  
  - 비밀번호 불일치 → 401  
  - 탈퇴 계정 로그인 → 401  
- 전체 인증 흐름에 대한 테스트 커버리지 확보  

### 3. OAuth2.0 (Google) 로그인 연동
- 구글 계정으로 로그인 성공 시:  
  - 사용자 정보 매핑 (기존 계정 없으면 자동 회원가입)  
  - JWT 발급 후 httpOnly 쿠키 저장  
  - 클라이언트 루트 페이지(`/`)로 리디렉트  
- 로그인 실패 시 `OAuth2AuthenticationFailureHandler` 작동  

### 4. 홈 리디렉트 기능 추가
- 인증 성공 시 `OAuth2AuthenticationSuccessHandler` 내부에서 `response.sendRedirect("/")` 처리  
- 쿠키 발급 이후 클라이언트 홈으로 리디렉트되며 인증 상태 유지 가능  

---

## 🧪 테스트 및 검증 내역

| 항목 | 결과 |
|------|------|
| 로컬 로그인 성공 | ✅ |
| 잘못된 이메일/비밀번호/탈퇴 계정 → 401 반환 | ✅ |
| OAuth2.0 로그인 성공 후 쿠키 저장 + 홈 리디렉트 | ✅ |
| 통합 테스트 정상 작동 | ✅ |
| 인증된 상태에서 사용자 정보 조회 | ✅ |
| SecurityFilterChain 변경 후 MockMvc 통합 테스트 호환성 | ✅ |

---

## 🤯 어려웠던 점 & 해결 방법

### ✅ OAuth2 로그인 후 리다이렉트 무한 루프 현상
- 원인: `OAuth2AuthenticationSuccessHandler`가 등록되지 않아 실행되지 않음  
- 해결: Google Client ID 확인 및 application.yml 매핑 확인, `SecurityConfig`에서 `successHandler` 명시적으로 등록

---

## 💡 느낀 점

- 인증 로직은 복잡도가 높고, 작은 설정 차이로도 큰 문제(무한 루프, 쿠키 누락 등)를 일으킬 수 있다.  
- 통합 테스트는 기능의 안정성을 확보하는 데 매우 중요하다. 특히 예외 상황을 체계적으로 커버하면 유지보수에 강한 구조가 된다.  
- OAuth2.0 연동은 단순해 보여도 내부적으로 처리할 항목이 많으며, 클라이언트까지 고려한 전체 흐름 정리가 중요하다.  

---

## 🏁 다음 목표

- [ ] Kakao / Naver 등 소셜 로그인 추가 연동  
- [ ] JWT 자동 갱신을 위한 Refresh Token 전략 설계 및 적용  
- [ ] 인증된 사용자 상태의 전역 AuthContext 처리 (프론트)  
- [ ] 회원 탈퇴 시 Refresh Token 삭제 로직 연동  
