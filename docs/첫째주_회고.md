# 로그인 기능 개발 회고 (feature/login)

## 잘한 점

- **JWT 기반 로그인/회원가입 흐름 설계**
  - `/signup`, `/login`, API 흐름 정리
  - JWT 발급, 검증, 인증 필터 연동까지 구현

- **Swagger 연동 및 API 테스트 완료**
  - Postman 및 Swagger를 통해 인증 API 흐름을 실제 테스트
  - `Authorization: Bearer` 방식 적용 및 성공 응답 확인

- **공통 응답 및 예외처리 구조 적용**
  - `ApiResponse`, `ApiErrorSchema`, `ErrorCode`를 글로벌 예외 메시지 처리
  
- **기능 단위 개발 완료 후 PR 작성 및 리뷰 요청**
  - `feature/login` 브랜치에서 분리 개발 후 GitHub PR 등록
  - 브랜치 방향 오류(`main` → `develop`)도 빠르게 인지하고 수정

- **문서화 기반 협업 준비**
  - `auth-flow.md` 문서를 기반으로 전체 인증 흐름 정리
  - 다른 팀원과 공유 가능한 형태로 남길 준비 완료

---

## 😕 아쉬웠던 점

- **초기 PR 브랜치 설정 오류**
  - 기본 브랜치가 `main`으로 되어 있어 잘못된 방향으로 PR 생성
  - 리뷰 이전에 인지해서 수정했지만, 초기 설정 확인이 부족했음

- **GitHub remote 설정 미비**
  - GitHub remote 권한 오류(`403 error`) 발생
  - 초대 여부 확인이나 로컬 Git 세팅이 미리 점검되지 않음

- **복구/탈퇴 관련 기능 미완료**
  - `/users/recover`와 관련된 기능은 아직 논의 필요
  - 로그인 기능 전체 흐름 상 탈퇴/복구 흐름 고민 필요

- **단위테스트 미흡**
  - Postman으로 테스트는 완료했지만, JUnit 기반의 단위 테스트는 미흡
  - 서비스 계층의 각 기능 단위 테스트, 컨트롤러단 및 기능 중심의 통합 테스트 필요

- **시큐리티 설정 미흡**
  - 다른 팀원이 접근했을 때 효과적으로 적용 가능한 설정 구조 고민 필요

- **인증 로직에 따른 ERD 보완과 보안 고민 필요**
  - 추후 실명인증 기능(전문가 도메인) 구현 가능성에 따라 실명 인증 기능, 그리고 그에 따른 새로운 ERD(실제 사람에 대한 데이터)의 생성과 관리에 대한 고민 필요

- **OAuth2.0 관련 기능 미구현**
  - 구글, 네이버, 카카오 순으로 빠르게 구현 필요
---

## 개선할 점 (Action Items)

- [ ] **GitHub 협업 환경 사전 점검**
  - remote 권한/초대 여부/기본 브랜치 설정 확인 후 PR 생성 습관화

- [ ] **OAuth2.0 확장 전, 로컬 로그인 플로우 테스트 보완**
  - 실패 케이스, 토큰 만료, 필터 예외 처리 등 검증 강화

- [ ] **기능 단위마다 `/docs/` 내 회고 파일 함께 생성**
  - `retrospective-login.md`, `retrospective-oauth.md`처럼 각 기능별로 문서화
  - PR에 링크해 협업 시 흐름 공유

- [ ] **누락된 `/users/recover` 관련 플로우 조속히 구현**
  - 탈퇴 복구 흐름까지 포함되어야 인증 시스템이 완성됨

- [ ] **OAuth2.0 확장 계획**
  - Kakao/Naver/Google 순으로 확장하며 `loginMethod` 처리, 토큰 전략 통합 계획 필요

---

> **총평**  
> 어플리케이션의 가장 기반이 되는 기술이라고 생각해서 로그인 기능을 맡았는데, 고민해야 할 부분이 정말 많다는 것을 느꼈습니다. 최대한 모든 시나리오를 포괄할 수 있는 로그인 기능을 구현해보겠습니다.