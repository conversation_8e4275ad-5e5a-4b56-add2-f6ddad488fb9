{"name": "frontend-test", "version": "0.1.0", "private": true, "dependencies": {"@stomp/stompjs": "^7.1.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "axios": "^1.9.0", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "react-spinners": "^0.17.0", "sockjs-client": "^1.6.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "proxy": "http://localhost:8080", "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@types/date-fns": "^2.6.3", "@types/react-router-dom": "^5.3.3", "@types/sockjs-client": "^1.5.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.5", "tailwindcss": "^3.4.1"}}