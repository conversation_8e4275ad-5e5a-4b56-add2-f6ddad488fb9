package moneybuddy.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moneybuddy.global.enums.ErrorCode;
import moneybuddy.global.exception.CustomException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class JwtTokenProvider {

    @Value("${jwt.secret}")
    private String secretKeyPlain; // 원래 설정에서 불러오는 문자열

    private SecretKey secretKey; // 실제 사용할 시크릿 키 객체

    private static final long EXPIRATION_TIME = 1000L * 60 * 60 * 24; // 24시간
    private static final long REFRESH_TOKEN_EXPIRATION = 1000L * 60 * 60 * 24 * 14; // 14일

    @PostConstruct
    public void init() {
        // 평문 문자열을 SecretKey로 변환
        this.secretKey = Keys.hmacShaKeyFor(secretKeyPlain.getBytes(StandardCharsets.UTF_8));
    }

    public String createToken(Long userId, String role) {
        Date now = new Date();
        return Jwts.builder()
                .setSubject(userId.toString())
                .claim("role", role)
                .setIssuedAt(now)
                .setExpiration(new Date(now.getTime() + EXPIRATION_TIME))
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }

    public String createRefreshToken(Long userId) {
        Date now = new Date();
        return Jwts.builder()
                .setSubject(userId.toString())
                .setIssuedAt(now)
                .setExpiration(new Date(now.getTime() + REFRESH_TOKEN_EXPIRATION))
                .signWith(secretKey, SignatureAlgorithm.HS256)
                .compact();
    }

    public Long getUserId(String token) {
        try {
            return Long.parseLong(
                    Jwts.parserBuilder()
                        .setSigningKey(secretKey)
                        .build()
                        .parseClaimsJws(token)
                        .getBody()
                        .getSubject()
            );
        } catch (JwtException e) {
            log.warn("❌ 토큰에서 사용자 ID 추출 실패: {}", e.getMessage());
            throw new CustomException(ErrorCode.INVALID_JWT_FORMAT);
        }
    }

    public boolean validateToken(String token) {
        try {
            Jwts.parserBuilder()
                    .setSigningKey(secretKey)
                    .build()
                    .parseClaimsJws(token);
            return true;
        } catch (io.jsonwebtoken.security.SignatureException e) {
            log.warn("유효하지 않은 서명: {}", token);
            throw new CustomException(ErrorCode.INVALID_JWT_SIGNATURE);
        } catch (ExpiredJwtException e) {
            log.warn("토큰 만료: {}", token);
            throw new CustomException(ErrorCode.EXPIRED_JWT_TOKEN);
        } catch (MalformedJwtException e) {
            log.warn("⚠잘못된 JWT 구조: {}", token);
            throw new CustomException(ErrorCode.INVALID_JWT_FORMAT);
        } catch (io.jsonwebtoken.io.DecodingException e) {
            log.warn("Base64 디코딩 실패: {}", token);
            throw new CustomException(ErrorCode.JWT_DECODING_FAILED);
        } catch (Exception e) {
            log.warn("기타 JWT 예외: {}", e.getMessage());
            throw new CustomException(ErrorCode.UNKNOWN_JWT_ERROR);
        }
    }
}
